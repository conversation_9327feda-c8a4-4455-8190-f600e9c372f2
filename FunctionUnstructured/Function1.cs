using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Python.Runtime;
using System;

namespace FunctionUnstructured
{
    public class Function1
    {
        private readonly ILogger<Function1> _logger;

        public Function1(ILogger<Function1> logger)
        {
            _logger = logger;
        }

        [Function("Function1")]
        public IActionResult Run([HttpTrigger(AuthorizationLevel.Function, "get", "post")] HttpRequest req)
        {
            try
            {
                using (Py.GIL())
                {
                    // Add current directory to Python path to ensure example.py can be found
                    dynamic sys = Py.Import("sys");
                    string currentDirectory = Environment.CurrentDirectory;
                    _logger.LogInformation($"Current directory: {currentDirectory}");

                    // Add current directory to Python path if not already present
                    dynamic pathList = sys.path;
                    bool pathExists = false;
                    foreach (var path in pathList)
                    {
                        if (path.ToString() == currentDirectory)
                        {
                            pathExists = true;
                            break;
                        }
                    }

                    if (!pathExists)
                    {
                        sys.path.append(currentDirectory);
                        _logger.LogInformation($"Added {currentDirectory} to Python path");
                    }

                    // Import and execute the example.py module
                    _logger.LogInformation("Importing example.py module...");
                    dynamic example = Py.Import("example");

                    _logger.LogInformation("Calling hello_world() function...");
                    dynamic result = example.hello_world();

                    _logger.LogInformation($"Python function returned: {result}");
                    _logger.LogInformation("C# HTTP trigger function processed a request successfully.");

                    return new OkObjectResult(new {
                        message = "Python script executed successfully",
                        result = result.ToString(),
                        timestamp = DateTime.UtcNow
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing Python script: {ErrorMessage}", ex.Message);
                return new BadRequestObjectResult(new {
                    error = "Failed to execute Python script",
                    details = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }
    }
}
