using System;
using Python.Runtime;

namespace TestConsole
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing Python integration...");
            
            try
            {
                // Set Python DLL path
                Runtime.PythonDLL = @"C:\Program Files (x86)\Microsoft Visual Studio\Shared\Python39_64\python39.dll";
                
                // Initialize Python
                PythonEngine.Initialize();
                
                using (Py.GIL())
                {
                    // Add current directory to Python path
                    dynamic sys = Py.Import("sys");
                    string currentDirectory = Environment.CurrentDirectory;
                    Console.WriteLine($"Current directory: {currentDirectory}");
                    
                    // Add current directory to Python path if not already present
                    dynamic pathList = sys.path;
                    bool pathExists = false;
                    foreach (var path in pathList)
                    {
                        if (path.ToString() == currentDirectory)
                        {
                            pathExists = true;
                            break;
                        }
                    }
                    
                    if (!pathExists)
                    {
                        sys.path.append(currentDirectory);
                        Console.WriteLine($"Added {currentDirectory} to Python path");
                    }
                    
                    // Import and execute the example.py module
                    Console.WriteLine("Importing example.py module...");
                    dynamic example = Py.Import("example");
                    
                    Console.WriteLine("Calling hello_world() function...");
                    dynamic result = example.hello_world();
                    
                    Console.WriteLine($"Python function returned: {result}");
                    Console.WriteLine("✅ Python integration test completed successfully!");
                    
                    // Test the same logic as in Function1.cs
                    var response = new { 
                        message = "Python script executed successfully", 
                        result = result.ToString(),
                        timestamp = DateTime.UtcNow
                    };
                    
                    Console.WriteLine($"Response object: {response}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            finally
            {
                PythonEngine.Shutdown();
            }
            
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
